import json

all_few_shot_dic = {
    "居里夫人_小行动": [
        {
            "query": "恐龙是怎么灭绝的？",
            "input": {
                "question": "恐龙灭绝的原因是什么？",
                "analysis": {
                    "核心科学原理": "目前最被科学家认可的理论是 “小行星撞击说” -- 约6600万年前，一颗直径 10 公里的小行星撞击地球，引发超级火山喷发、全球气候剧变。",
                    "关键现象解释": "导致气候灾难，撞击扬起的尘埃遮蔽阳光数年，导致地表温度骤降，同时酸雨破坏生态环境。",
                    "实验设计": {
                        "实验": "",
                        "安全提示": ""
                    },
                    "趣味延伸": "做个”能源小侦探“：观察家里有哪些电器在待机状态，比如电视、充电器，和家人讨论如何拔掉不用的插头。",
                }
            },
            "output": "约6600万年前，一颗直径10公里的小行星撞击地球，引发了超级火山喷发和全球气候剧变！尘埃遮天蔽日，让地球陷入长达数年的'核冬天'，酸雨破坏了生态环境，恐龙找不到食物慢慢消失了，这提醒我们要时刻爱护地球，让我们一起成为”地球小卫士“，回家后，观察家里有哪些电器在待机状态（比如电视、充电器），和家人讨论如何拔掉不用的插头，这不仅能节约能源，还能减少发电厂排放的温室气体哦！"
        }
    ],
    "居里夫人_冷知识": [
        {
            "query": "恐龙是什么原因灭绝的？",
            "input": {
                "question": "恐龙灭绝的原因是什么？",
                "analysis": {
                    "核心科学原理": "目前最被科学家认可的理论是 “小行星撞击说” -- 约6600万年前，一颗直径 10 公里的小行星撞击地球，引发超级火山喷发、全球气候剧变。",
                    "关键现象解释": "导致气候灾难，撞击扬起的尘埃遮蔽阳光数年，导致地表温度骤降，同时酸雨破坏生态环境。",
                    "实验设计": {
                        "实验": "",
                        "安全提示": ""
                    },
                    "趣味延伸": "你知道吗？小行星撞地球时，冲击波比音速还快，恐龙可能还没听到声音就被气浪掀飞了。"
                }
            },
            "output": "恐龙灭绝最可能的原因是一颗直径10公里的小行星撞击地球！大约6600万年前，一颗小行星撞上了现今墨西哥尤卡坦半岛，引发了一系列灾难：撞击产生的尘埃遮蔽阳光数年，让地球变得又冷又暗；接着酸雨降临，破坏生态环境，最终导致恐龙难以生存。有趣的是，根据科学家推测，撞击产生的冲击波传播速度甚至超过了音速，这意味着恐龙还没听见撞击声，就被强大的气浪掀翻了！"
        }],
        "达尔文_小行动": [
            {
                "query": "恐龙为什么会灭绝？",
                "input": {
                    "question": "恐龙灭绝的原因是什么？",
                    "analysis": {
                        "核心科学原理": "目前最被科学家认可的理论是 “小行星撞击说” -- 约6600万年前，一颗直径 10 公里的小行星撞击地球，引发超级火山喷发、全球气候剧变。",
                        "关键现象解释": "植物因光照不足大量死亡，植食性恐龙饿死，肉食性恐龙随之灭绝，而小型哺乳动物靠杂食和耐寒特性幸存。",
                        "实验设计": {
                            "实验": "",
                            "安全提示": ""
                        },
                        "趣味延伸": "请选择一种你喜欢的动物或植物，为它设计一个”安全屋“。设计时，可以思考动物或植物适合的生存环境以及保护方法。"
                    }
                },
                "output": "科学家们认为，恐龙灭绝最可能的原因是大约是6600万年前，一颗直径10公里的小行星撞击地球，引发了可怕的连锁反应：火山喷发，火山灰遮天蔽日，植物缺少阳光大量死亡，恐龙缺乏食物导致死亡。但有趣的是，体型较小、食性多样的哺乳动物却顽强地存活了下来。这提醒着我们保护生物多样性的重要。现在，请你选择一种动物或植物，为它设计一个”安全屋“。设计时注意思考动物或植物适合的生存环境及保护方法。",
            }
    ],
    "达尔文_冷知识": [
        {
            "query": "恐龙是因为什么而灭绝的？",
            "input": {
                "question": "恐龙灭绝的原因是什么？",
                "analysis": {
                    "核心科学原理": "目前最被科学家认可的理论是 “小行星撞击说” -- 约6600万年前，一颗直径 10 公里的小行星撞击地球，引发超级火山喷发、全球气候剧变。",
                    "关键现象解释": "植物因光照不足大量死亡，植食性恐龙饿死，肉食性恐龙随之灭绝，而小型哺乳动物靠杂食和耐寒特性幸存。",
                    "实验设计": {
                        "实验": "",
                        "安全提示": ""
                    },
                    "趣味延伸": "你知道吗？蟑螂比恐龙活得好，恐灭绝了，但蟑螂靠吃枯叶、腐木甚至恐龙的粪便活了下来，它们能忍受辐射、高温和饥饿，连小行星撞击都没消灭它们。"
                }
            },
            "output": "约6600万年前，一颗直径10公里的小行星，撞击墨西哥尤卡坦半岛。这场撞击引发连锁灾难：火山喷发的烟尘遮天蔽日数年，植物因缺少阳光成片死亡。恐龙因缺少食物灭绝，但你知道吗？当时不起眼的蟑螂竟然和恐龙一起经历了这场浩劫！这些”小强“靠吃枯枝烂叶，甚至恐龙粪便，活了下来。恐龙称霸地球1.6亿年灭绝了，而蟑螂却悄悄活了3亿年至今，这场远古灾难告诉我们：生物多样性是地球的”保险箱“。不同物种各有所长，才能应对突发危机。"
        }
],
    "居里夫人_冷知识":[
        {
            "query": "什么是热对流？",
            "input": {
                "question": "什么是热对流？",
                "analysis": {
                    "核心科学原理": "热对流是液体/气体通过自身流动传递热量的方式。",
                    "关键现象解释": "比如煮开水时热水上升、冷水下降形成的循环。",
                    "实验设计": {
                        "实验": "观察实验 (材料：透明杯 + 色素 + 热水):##1. 杯底注入冷水，轻轻加入一滴热水 (染红色)##2. 观察红色热水像小火箭一样向上窜！",
                        "安全提示": "用温水代替开水防烫伤！"
                    },
                    "趣味延伸": "煮火锅时，红油锅底比清汤先沸腾 —— 因为油层像“保温毯”锁住热量，形成上下温差更大的对流，加速加热！"
                }
            },
            "output": "热对流就是液体或气体自己流动传递热量的方式，就像煮开水时热水上升、冷水下降形成的循环。我们可以做个有趣的小实验：先在透明杯里倒入冷水，等水面平静后，用滴管轻轻往中央滴一滴染红的温水。看！红色热水立刻像小火箭一样往上窜，这是因为热水比较”轻“会上升，冷水比较”重“会下沉，形成了流动的”热流圈“（小心烫伤，记得要用温水哦）。生活中煮火锅时，红油锅总是比清汤锅沸腾得快，就是因为油层像条”保温毯“，让热对流更猛烈呢！"
        }
    ],
    "达尔文_冷知识":[
        {
            "query": "什么是热对流？",
            "input": {
                "question": "什么是热对流？",
                "analysis": {
                    "核心科学原理": "热对流是液体/气体通过自身流动传递热量的方式。",
                    "关键现象解释": "比如煮开水时热水上升、冷水下降形成的循环。",
                    "实验设计": {
                        "实验": "观察实验 (材料：透明杯 + 色素 + 热水):##1. 杯底注入冷水，轻轻加入一滴热水 (染红色)##2. 观察红色热水像小火箭一样向上窜！",
                        "安全提示": "用温水代替开水防烫伤！"
                    },
                    "趣味延伸": "变色龙晒太阳时，会故意把深色皮肤朝向来升温更快，深色吸收更多热量，体内血液加速对流循环。"
                }
            },
            "output": "热对流是液体或气体自己流动传热的有趣现象！秋天落叶经常在墙角打转，就是因为地面热气上升，树荫冷空气过来，形成小旋风，我们做个实验：往冷水里滴一滴染红的温水（小心烫伤，记得用温水哦），红色热水像小火箭”嗖“地往上冲！因为热水轻会往上跑，冷水重会往下沉。你知道吗？变色龙晒太阳时会把深色皮肤转向阳光，这样能吸收更多热量，让血液流动更快呢。"

        }
    ]
}

def build_input(query, data, key, few_shot_str):
    role, type = key.split('_')
    return f'''
现在请你扮演{role}, 参考样例，根据检索的参考信息，回答学生的问题，并按{type}进行趣味延申。
# 核心任务:
1.  **输入格式**: 你将收到一个包含特定科学问题{query}为输入对象。
2.  **输出要求**: 你必须严格按照'参考信息'和下面的“答案配方”生成答案。可以小幅度修改“答案配方”中的内容，但禁止大幅度修改。
3.  **内容融合与叙事逻辑**: 每个答案都必须融合相应配方中指定的所有字段内容，不得遗漏。**关键在于，必须遵循“核心科学原理 → 关键现象解释 → 实验设计 → 趣味延伸”的叙事顺序，不能生硬地拼接各个部分，而应使用承上启下的过渡语，将它们融合成一个行文流畅、逻辑严谨的有机整体。**
4.  **语言风格**: 答案应保持科普风格，生动有趣，易于理解。
5.  **实验/小行动总结**: 在每个包含实验的答案中，紧跟在`实验设计`内容描述之后，必须无缝衔接一句精炼的、画龙点睛的半句话或一句话总结。这句总结的作用是揭示该实验现象背后的科学原理（该原理源自`核心科学原理`或`关键现象解释`），从而将实验现象与科学知识点巧妙地联系起来，让总结自然地融入段落，增强科普效果。
# 参考信息
{data}

#答案配方
*   **内容来源**：{data['analysis']}.核心科学原理+{data['analysis']}.关键现象解释+{data['analysis']}.实验设计+{data['analysis']}.趣味延伸

# 关键约束条件 (必须严格遵守)
1.  **答案生成规则 (最高优先级)**: 此规则是决定一个答案最终是否为空的**唯一**标准。
    *   在为任何一个答案生成内容前，**必须检查其配方中对应的最终趣味延伸字段**。
    *   **如果该字段为空字符串 `""`**，则最终输出的对象中对应的整个键的值也**必须是空字符串 `""`**，并跳过该答案的所有文本生成。
    *   此规则对所有答案均适用，且独立判断。例如，'输出'是否为空，**只取决于**`analysis.趣味延伸`是否为空，**与`实验设计`字段是否有内容完全无关**。
2.  **移除问题**: 生成的所有答案文本中，**绝对不能包含**原始的`question`字段内容。答案应直接切入解释。
3.  **语言风格**:
    * **通俗生动**: 避免使用生僻术语。
    * **多用比喻**: 善于运用生活中的常见事物进行类比，帮助理解。
    * **启发互动**: 语言富有启发性，鼓励读者亲身观察或动手尝试。
    * **叙事化融合 (Narrative Fusion)**: 严格按照“原理→解释→实验→延伸”的顺序，将知识模块编织成连贯的解说，确保起承转合自然流畅，不是简单地分点罗列，确保段落内部的起承转合自然流畅。
4.  **开篇多样性 (Opening Variety)**: 输出的开篇句（源自`核心科学原理`）**禁止完全相同**。必须在保持原意不变的前提下，对措辞进行微调，以增强内容的多样性和可读性。
5.  **字数限制 (Word Count Limit)**: 为了确保答案详实且易读，每个生成的非空答案文本长度应力求控制在**190至230字**的目标范围内。
    *   **核心原则**: 在融合所有必要信息并保持行文流畅的前提下，进行适度扩充或精简以达到目标字数。
    *   **例外情况**: 如果配方中的源内容本身非常简短，即使自然融合后也难以达到190字的下限，则**应以内容的忠实性和自然流畅为最高优先级**，不必强行添加无关信息来凑字数。

# 趣味延申类型
{type}

# 学生输入问题
{query}

{few_shot_str}

'''

def get_few_shot_str(idx, query, input, output):
    return f'''
    #样例{idx}
    ##样例{idx}输入
    {query}
    ##样例{idx}参考信息
    {input}
    ##样例{idx}输出
    {output}
    '''

def get_query_from_mapping(question):
    question_mapping_path = r'D:\ProJects\kexue\fanzhang39\codes\Deduplicate\sentence_bert_deduplication\outputs\merged_outputs\output_0.9\merged_datas_round1_0.95_mapping.json'
    with open(question_mapping_path, 'r', encoding='utf-8') as f:
        all_data = json.load(f)

    for data in all_data:
        print(json.load(data))
    #     if

if __name__ == '__main__':
    question = '黑洞是什么？'
    query = get_query_from_mapping(question)


# if __name__ == '__main__':
#     res = []
#     keys = ["居里夫人_小行动", "居里夫人_冷知识", "达尔文_小行动", "达尔文_冷知识"]
#     for data in data_lst:
#         for key in keys:
#             dic = {}
#             dic['id'] = 'xx'
#             query = get_query_from_mapping(data['question'])
#
#     few_shot_str_lst = []
#     for idx, few_shot_dic in enumerate(all_few_shot_dic[key], start=1):
#       few_shot_str = get_few_shot_str(idx, few_shot_dic['query'], few_shot_dic['input'], few_shot_dic['output'])
#       few_shot_str_lst.append(few_shot_str.strip())
#     few_shot_str = '\n'.join(few_shot_str_lst)
#
#     input = build_input(query, data, key, few_shot_str)
#     dic['input'] = input
#     dic['target'] = data['output'][key]
#     res.append(dic)